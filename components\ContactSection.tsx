import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import { toast } from 'sonner@2.0.3';

import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock,
  Linkedin,
  Github,
  Twitter,
  Dribbble,
  Send,
  Loader2,
  CheckCircle,
  User,
  MessageSquare,
  FileText,
  Star,
  Zap,
  Shield,
  HeartHandshake,
  Headphones,
  Terminal,
  Wifi,
  Globe,
  Coffee
} from 'lucide-react';

const contactMethods = [
  {
    icon: Mail,
    title: 'Email',
    value: '<EMAIL>',
    description: 'Drop me a line anytime',
    action: 'mailto:<EMAIL>',
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/10',
    command: 'echo "<EMAIL>" | mail'
  },
  {
    icon: Phone,
    title: 'Phone',
    value: '+****************',
    description: 'Call for urgent matters',
    action: 'tel:+15551234567',
    color: 'text-green-400',
    bgColor: 'bg-green-500/10',
    command: 'curl -X CALL tel:+15551234567'
  },
  {
    icon: MapPin,
    title: 'Location',
    value: 'San Francisco, CA',
    description: 'Available for remote work',
    action: null,
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/10',
    command: 'whereami --remote-friendly'
  },
  {
    icon: Clock,
    title: 'Response Time',
    value: '< 24 hours',
    description: 'I reply quickly',
    action: null,
    color: 'text-orange-400',
    bgColor: 'bg-orange-500/10',
    command: 'uptime --response-avg'
  },
];

const socialLinks = [
  { 
    name: 'LinkedIn', 
    icon: Linkedin, 
    url: '#', 
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/30',
    command: 'open linkedin.com/in/vicky'
  },
  { 
    name: 'GitHub', 
    icon: Github, 
    url: '#', 
    color: 'from-gray-600 to-gray-700',
    bgColor: 'bg-gray-500/10',
    borderColor: 'border-gray-500/30',
    command: 'git clone github.com/vicky'
  },
  { 
    name: 'Twitter', 
    icon: Twitter, 
    url: '#', 
    color: 'from-blue-400 to-blue-500',
    bgColor: 'bg-blue-400/10',
    borderColor: 'border-blue-400/30',
    command: 'tweet @vickymosafan'
  },
  { 
    name: 'Dribbble', 
    icon: Dribbble, 
    url: '#', 
    color: 'from-pink-500 to-rose-500',
    bgColor: 'bg-pink-500/10',
    borderColor: 'border-pink-500/30',
    command: 'curl dribbble.com/vicky'
  },
];

const whyWorkWithMe = [
  {
    icon: Star,
    title: 'Full-stack expertise from frontend to backend',
    color: 'text-yellow-400',
    command: './skills --fullstack'
  },
  {
    icon: Zap,
    title: 'Clean, maintainable, and scalable code',
    color: 'text-blue-400',
    command: 'npm run code:quality'
  },
  {
    icon: Shield,
    title: 'Strong focus on user experience and performance',
    color: 'text-green-400',
    command: 'yarn test:performance'
  },
  {
    icon: HeartHandshake,
    title: 'Regular communication and project updates',
    color: 'text-purple-400',
    command: 'slack --daily-standup'
  },
  {
    icon: Headphones,
    title: 'Post-launch support and maintenance',
    color: 'text-orange-400',
    command: 'service start support'
  },
];

interface FormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
}

export default function ContactSection() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hoveredMethod, setHoveredMethod] = useState<number | null>(null);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    } else if (formData.subject.trim().length < 5) {
      newErrors.subject = 'Subject must be at least 5 characters';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('Message sent successfully! I\'ll get back to you soon.');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white relative overflow-hidden">
      {/* Terminal Grid Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Badge className="mb-4 bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30 font-mono">
              <Mail className="w-3 h-3 mr-2" />
              $ ./contact-me.sh
            </Badge>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl lg:text-6xl font-mono mb-6"
          >
            <span className="text-gray-500">#</span>{' '}
            <span className="text-white">Let's Work</span>
            <br />
            <span className="text-green-400">Together</span>
            <motion.span
              animate={{ opacity: [1, 0, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="text-green-400"
            >
              _
            </motion.span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto font-mono"
          >
            <span className="text-gray-500">//</span> Ready to bring your project to life?
            <br />
            <span className="text-gray-500">//</span> I'm always excited to collaborate on{' '}
            <span className="text-blue-400">innovative projects</span>
            <br />
            <span className="text-gray-500">//</span> and help turn your ideas into{' '}
            <span className="text-yellow-400">reality</span>.
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Contact Methods Terminal */}
            <div className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                </div>
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-mono text-gray-300">contact-info.json</span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-mono text-blue-400 mb-6 flex items-center gap-2">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  >
                    <Wifi className="w-5 h-5" />
                  </motion.div>
                  $ cat contact.json
                </h3>
                
                <div className="grid gap-4">
                  {contactMethods.map((method, index) => {
                    const IconComponent = method.icon;
                    return (
                      <motion.div
                        key={method.title}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        onMouseEnter={() => setHoveredMethod(index)}
                        onMouseLeave={() => setHoveredMethod(null)}
                        whileHover={{ scale: 1.02, y: -2 }}
                        className="cursor-pointer"
                        onClick={() => method.action && window.open(method.action, '_blank')}
                      >
                        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 hover:border-green-500/30 transition-all group">
                          <div className="flex items-start gap-3">
                            <motion.div 
                              className={`p-2 rounded-lg ${method.bgColor} border border-gray-600 group-hover:scale-110 transition-transform`}
                              animate={{ 
                                scale: hoveredMethod === index ? 1.1 : 1,
                                rotate: hoveredMethod === index ? [0, -5, 5, 0] : 0 
                              }}
                              transition={{ duration: 0.3 }}
                            >
                              <IconComponent className={`w-4 h-4 ${method.color}`} />
                            </motion.div>
                            <div className="flex-1">
                              <h4 className="font-mono text-white group-hover:text-green-400 transition-colors">
                                {method.title}
                              </h4>
                              <p className="text-sm font-mono text-gray-300">{method.value}</p>
                              <p className="text-xs font-mono text-gray-500">{method.description}</p>
                              <div className="text-xs font-mono text-gray-600 mt-1">
                                $ {method.command}
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Social Links Terminal */}
            <div className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                </div>
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-mono text-gray-300">social-links.sh</span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-mono text-purple-400 mb-4 flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  $ ls -la socials/
                </h3>
                <div className="flex gap-3">
                  {socialLinks.map((social, index) => {
                    const IconComponent = social.icon;
                    return (
                      <motion.a
                        key={social.name}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        whileHover={{ scale: 1.1, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className={`p-3 rounded-lg ${social.bgColor} border ${social.borderColor} hover:bg-opacity-80 transition-all group relative overflow-hidden`}
                      >
                        <IconComponent className="w-5 h-5 text-gray-300 group-hover:text-white relative z-10" />
                        
                        {/* Tooltip */}
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          whileHover={{ opacity: 1, y: 0 }}
                          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs font-mono px-2 py-1 rounded border border-gray-600"
                        >
                          {social.command}
                        </motion.div>
                      </motion.a>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Why Work With Me Terminal */}
            <div className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                </div>
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-mono text-gray-300">why-hire-me.md</span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-mono text-yellow-400 mb-4 flex items-center gap-2">
                  <Coffee className="w-5 h-5" />
                  $ cat benefits.txt
                </h3>
                <div className="space-y-3">
                  {whyWorkWithMe.map((item, index) => {
                    const IconComponent = item.icon;
                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        className="flex items-center gap-3 group hover:bg-gray-800/50 p-2 rounded-lg transition-colors"
                        whileHover={{ x: 5 }}
                      >
                        <motion.div 
                          className="group-hover:scale-110 transition-transform"
                          whileHover={{ rotate: [0, -10, 10, 0] }}
                          transition={{ duration: 0.5 }}
                        >
                          <IconComponent className={`w-4 h-4 ${item.color}`} />
                        </motion.div>
                        <div className="flex-1">
                          <span className="text-sm font-mono text-gray-300 group-hover:text-white transition-colors">
                            {item.title}
                          </span>
                          <div className="text-xs font-mono text-gray-600">
                            $ {item.command}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Contact Form Terminal */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                </div>
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-mono text-gray-300">send-message.form</span>
                </div>
              </div>

              <div className="p-8">
                <h3 className="text-lg font-mono text-green-400 mb-6 flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  $ nano compose-message.txt
                </h3>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="flex items-center gap-1 text-sm font-mono text-gray-300">
                        <User className="w-3 h-3" />
                        --name *
                      </Label>
                      <motion.div
                        whileFocus={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Input 
                          id="name" 
                          placeholder="Your name"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          className={`bg-gray-800 border-gray-600 text-white font-mono ${errors.name ? 'border-red-500' : ''} transition-colors`}
                        />
                      </motion.div>
                      <AnimatePresence>
                        {errors.name && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="text-xs text-red-400 flex items-center gap-1 font-mono"
                          >
                            <span>Error:</span> {errors.name}
                          </motion.p>
                        )}
                      </AnimatePresence>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email" className="flex items-center gap-1 text-sm font-mono text-gray-300">
                        <Mail className="w-3 h-3" />
                        --email *
                      </Label>
                      <motion.div
                        whileFocus={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Input 
                          id="email" 
                          type="email" 
                          placeholder="<EMAIL>"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          className={`bg-gray-800 border-gray-600 text-white font-mono ${errors.email ? 'border-red-500' : ''} transition-colors`}
                        />
                      </motion.div>
                      <AnimatePresence>
                        {errors.email && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="text-xs text-red-400 flex items-center gap-1 font-mono"
                          >
                            <span>Error:</span> {errors.email}
                          </motion.p>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="subject" className="flex items-center gap-1 text-sm font-mono text-gray-300">
                      <FileText className="w-3 h-3" />
                      --subject *
                    </Label>
                    <motion.div
                      whileFocus={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Input 
                        id="subject" 
                        placeholder="Project discussion"
                        value={formData.subject}
                        onChange={(e) => handleInputChange('subject', e.target.value)}
                        className={`bg-gray-800 border-gray-600 text-white font-mono ${errors.subject ? 'border-red-500' : ''} transition-colors`}
                      />
                    </motion.div>
                    <AnimatePresence>
                      {errors.subject && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="text-xs text-red-400 flex items-center gap-1 font-mono"
                        >
                          <span>Error:</span> {errors.subject}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="message" className="flex items-center gap-1 text-sm font-mono text-gray-300">
                      <MessageSquare className="w-3 h-3" />
                      --message *
                    </Label>
                    <motion.div
                      whileFocus={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Textarea
                        id="message"
                        placeholder="Tell me about your project..."
                        className={`min-h-[120px] bg-gray-800 border-gray-600 text-white font-mono ${errors.message ? 'border-red-500' : ''} transition-colors`}
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                      />
                    </motion.div>
                    <AnimatePresence>
                      {errors.message && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="text-xs text-red-400 flex items-center gap-1 font-mono"
                        >
                          <span>Error:</span> {errors.message}
                        </motion.p>
                      )}
                    </AnimatePresence>
                    <div className="text-right">
                      <span className="text-xs text-gray-500 font-mono">
                        {formData.message.length}/500 characters
                      </span>
                    </div>
                  </div>
                  
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button 
                      type="submit" 
                      disabled={isSubmitting}
                      className="w-full bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30 py-6 relative overflow-hidden group font-mono"
                    >
                      <AnimatePresence mode="wait">
                        {isSubmitting ? (
                          <motion.div
                            key="loading"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center gap-2"
                          >
                            <Loader2 className="w-4 h-4 animate-spin" />
                            $ sending...
                          </motion.div>
                        ) : (
                          <motion.div
                            key="send"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center gap-2 group-hover:gap-3 transition-all"
                          >
                            <Send className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                            $ ./send-message.sh
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Button>
                  </motion.div>
                </form>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}