import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from './ui/dialog';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { 
  Terminal, 
  Code, 
  ExternalLink, 
  Github, 
  Calendar, 
  Clock, 
  Folder, 
  Play, 
  Eye,
  GitBranch,
  Star,
  CheckCircle,
  CircleDot
} from 'lucide-react';

const projects = [
  {
    id: 1,
    title: 'E-Commerce Platform',
    description: 'Full-stack e-commerce solution with real-time inventory management, payment processing, and admin dashboard.',
    fullDescription: 'A comprehensive e-commerce platform built with modern technologies. Features include user authentication, product catalog, shopping cart, payment integration with Stripe, real-time inventory updates, admin dashboard with analytics, and responsive design for mobile and desktop.',
    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',
    tech: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Docker'],
    category: 'Full Stack',
    status: 'Completed',
    timeline: '3 months',
    year: '2024',
    command: 'npm run ecommerce:start',
    features: [
      'User authentication and authorization',
      'Product catalog with search and filters',
      'Shopping cart and checkout process',
      'Stripe payment integration',
      'Admin dashboard with analytics',
      'Real-time inventory management',
      'Responsive design',
      'Email notifications'
    ],
    challenges: [
      'Implementing real-time inventory updates',
      'Optimizing database queries for large product catalogs',
      'Ensuring PCI compliance for payment processing'
    ],
    link: '#',
    github: '#',
  },
  {
    id: 2,
    title: 'Task Management App',
    description: 'Collaborative project management tool with real-time updates, team collaboration features, and advanced reporting.',
    fullDescription: 'A modern task management application designed for teams. Built with real-time collaboration features, drag-and-drop task organization, time tracking, project analytics, and integrations with popular tools.',
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop',
    tech: ['Next.js', 'TypeScript', 'Prisma', 'WebSocket', 'Tailwind'],
    category: 'Web Application',
    status: 'In Progress',
    timeline: '4 months',
    year: '2024',
    command: 'yarn dev:tasks',
    features: [
      'Real-time collaboration',
      'Drag-and-drop task management',
      'Time tracking and reporting',
      'Team member assignments',
      'Project templates',
      'File attachments',
      'Notification system',
      'Mobile-responsive design'
    ],
    challenges: [
      'Implementing real-time synchronization',
      'Optimizing performance for large datasets',
      'Creating intuitive drag-and-drop interface'
    ],
    link: '#',
    github: '#',
  },
  {
    id: 3,
    title: 'Analytics Dashboard',
    description: 'Data visualization platform with interactive charts, real-time metrics, and customizable reporting features.',
    fullDescription: 'An advanced analytics dashboard for business intelligence. Features interactive charts, real-time data updates, custom report generation, and integration with various data sources.',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop',
    tech: ['React', 'D3.js', 'Python', 'FastAPI', 'Redis'],
    category: 'Dashboard',
    status: 'Completed',
    timeline: '2 months',
    year: '2023',
    command: 'python run_dashboard.py',
    features: [
      'Interactive data visualizations',
      'Real-time metric updates',
      'Custom report builder',
      'Data export functionality',
      'Multiple chart types',
      'Responsive layouts',
      'Dark/light theme',
      'API integrations'
    ],
    challenges: [
      'Handling large datasets efficiently',
      'Creating smooth animations with D3.js',
      'Implementing real-time data streams'
    ],
    link: '#',
    github: '#',
  },
  {
    id: 4,
    title: 'Social Media Platform',
    description: 'Modern social networking app with real-time messaging, media sharing, and advanced privacy controls.',
    fullDescription: 'A feature-rich social media platform with focus on user privacy and engagement. Includes real-time messaging, media sharing, user feeds, and comprehensive privacy settings.',
    image: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=800&h=600&fit=crop',
    tech: ['Vue.js', 'Firebase', 'Node.js', 'Socket.io', 'AWS'],
    category: 'Social Platform',
    status: 'Completed',
    timeline: '6 months',
    year: '2023',
    command: 'vue-cli-service serve',
    features: [
      'User profiles and feeds',
      'Real-time messaging',
      'Media sharing (photos/videos)',
      'Privacy controls',
      'Friend connections',
      'Push notifications',
      'Content moderation',
      'Mobile app companion'
    ],
    challenges: [
      'Scaling real-time messaging',
      'Implementing content moderation',
      'Ensuring user privacy and security'
    ],
    link: '#',
    github: '#',
  },
  {
    id: 5,
    title: 'API Gateway Service',
    description: 'Microservices architecture with API gateway, authentication, rate limiting, and comprehensive documentation.',
    fullDescription: 'A robust API gateway solution for microservices architecture. Provides authentication, rate limiting, load balancing, and comprehensive API documentation.',
    image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=600&fit=crop',
    tech: ['Node.js', 'Express', 'MongoDB', 'Docker', 'Kubernetes'],
    category: 'Backend',
    status: 'Completed',
    timeline: '4 months',
    year: '2023',
    command: 'docker-compose up gateway',
    features: [
      'API gateway routing',
      'Authentication & authorization',
      'Rate limiting',
      'Load balancing',
      'API documentation',
      'Monitoring & logging',
      'Health checks',
      'Docker containerization'
    ],
    challenges: [
      'Designing scalable microservices architecture',
      'Implementing efficient rate limiting',
      'Creating comprehensive API documentation'
    ],
    link: '#',
    github: '#',
  },
  {
    id: 6,
    title: 'Mobile-First Web App',
    description: 'Progressive web application with offline capabilities, push notifications, and native app-like experience.',
    fullDescription: 'A progressive web application designed with mobile-first approach. Features offline functionality, push notifications, and app-like user experience across all devices.',
    image: 'https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=800&h=600&fit=crop',
    tech: ['React', 'PWA', 'Service Worker', 'IndexedDB', 'Workbox'],
    category: 'Mobile Web',
    status: 'In Progress',
    timeline: '3 months',
    year: '2024',
    command: 'npm run pwa:serve',
    features: [
      'Offline functionality',
      'Push notifications',
      'App-like experience',
      'Background sync',
      'Install prompts',
      'Performance optimization',
      'Responsive design',
      'Cross-platform compatibility'
    ],
    challenges: [
      'Implementing offline-first architecture',
      'Optimizing for mobile performance',
      'Managing service worker lifecycle'
    ],
    link: '#',
    github: '#',
  },
];

const categories = ['All', 'Full Stack', 'Web Application', 'Dashboard', 'Social Platform', 'Backend', 'Mobile Web'];
const statuses = ['All', 'Completed', 'In Progress'];

export default function ProjectsSection() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedProject, setSelectedProject] = useState<typeof projects[0] | null>(null);
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);

  const filteredProjects = projects.filter(project => {
    const categoryMatch = selectedCategory === 'All' || project.category === selectedCategory;
    const statusMatch = selectedStatus === 'All' || project.status === selectedStatus;
    return categoryMatch && statusMatch;
  });

  return (
    <section id="projects" className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white relative overflow-hidden">
      {/* Terminal Grid Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Badge className="mb-4 bg-purple-500/20 text-purple-400 border-purple-500/50 hover:bg-purple-500/30 font-mono">
              <Folder className="w-3 h-3 mr-2" />
              $ ls -la projects/
            </Badge>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl lg:text-6xl font-mono mb-6"
          >
            <span className="text-gray-500">#</span>{' '}
            <span className="text-white">Featured</span>
            <br />
            <span className="text-purple-400">Projects</span>
            <motion.span
              animate={{ opacity: [1, 0, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="text-purple-400"
            >
              _
            </motion.span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto font-mono"
          >
            <span className="text-gray-500">//</span> A selection of my recent work showcasing
            <br />
            <span className="text-gray-500">//</span> full-stack development skills, modern technologies,
            <br />
            <span className="text-gray-500">//</span> and{' '}
            <span className="text-yellow-400">creative problem-solving</span> approaches.
          </motion.p>
        </motion.div>

        {/* Terminal Filter Controls */}
        <div className="bg-gray-900 border border-gray-700 rounded-lg mb-12 overflow-hidden">
          {/* Terminal Header */}
          <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
            <div className="flex gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full" />
              <div className="w-3 h-3 bg-yellow-500 rounded-full" />
              <div className="w-3 h-3 bg-green-500 rounded-full" />
            </div>
            <div className="flex items-center gap-2">
              <Terminal className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-mono text-gray-300">project-filter.sh</span>
            </div>
          </div>

          {/* Filter Content */}
          <div className="p-6 space-y-6">
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-sm font-mono text-green-400 flex items-center gap-2">
                  <GitBranch className="w-4 h-4" />
                  $ filter --category=
                </span>
                {categories.map((category, index) => (
                  <motion.div
                    key={category}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.03 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Badge 
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 cursor-pointer transition-all font-mono ${
                        selectedCategory === category 
                          ? 'bg-purple-500/20 text-purple-400 border-purple-500/50 hover:bg-purple-500/30' 
                          : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                      }`}
                    >
                      {category}
                    </Badge>
                  </motion.div>
                ))}
              </div>
              
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-sm font-mono text-blue-400 flex items-center gap-2">
                  <CheckCircle className="w-4 h-4" />
                  $ filter --status=
                </span>
                {statuses.map((status, index) => (
                  <motion.div
                    key={status}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Badge 
                      onClick={() => setSelectedStatus(status)}
                      className={`px-3 py-1 cursor-pointer transition-all font-mono ${
                        selectedStatus === status 
                          ? 'bg-blue-500/20 text-blue-400 border-blue-500/50 hover:bg-blue-500/30' 
                          : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700 hover:border-gray-500'
                      }`}
                    >
                      {status}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <motion.div 
          layout 
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <AnimatePresence>
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="group"
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                <div className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden hover:border-purple-500/30 transition-all duration-300 h-full">
                  {/* Project Image */}
                  <div className="relative overflow-hidden">
                    <ImageWithFallback
                      src={project.image}
                      alt={project.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <motion.div 
                      className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"
                      initial={{ opacity: 0.5 }}
                      whileHover={{ opacity: 0.8 }}
                      transition={{ duration: 0.3 }}
                    />
                    
                    {/* Status and Category Badges */}
                    <div className="absolute top-4 left-4 right-4 flex justify-between">
                      <Badge className={`font-mono text-xs ${
                        project.status === 'Completed' 
                          ? 'bg-green-500/20 text-green-400 border-green-500/50' 
                          : 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50'
                      }`}>
                        {project.status === 'Completed' ? <CheckCircle className="w-3 h-3 mr-1" /> : <CircleDot className="w-3 h-3 mr-1" />}
                        {project.status}
                      </Badge>
                      <Badge className="bg-gray-800/90 text-gray-300 border-gray-600 font-mono text-xs backdrop-blur-sm">
                        {project.year}
                      </Badge>
                    </div>

                    {/* Hover Actions */}
                    <AnimatePresence>
                      {hoveredProject === project.id && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 20 }}
                          className="absolute bottom-4 left-4 right-4 flex gap-2"
                        >
                          <Button
                            size="sm"
                            className="flex-1 bg-purple-500/20 text-purple-400 border-purple-500/50 hover:bg-purple-500/30 font-mono backdrop-blur-sm"
                            onClick={() => setSelectedProject(project)}
                          >
                            <Eye className="w-3 h-3 mr-2" />
                            Details
                          </Button>
                          <Button
                            size="sm"
                            className="flex-1 bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30 font-mono backdrop-blur-sm"
                          >
                            <Play className="w-3 h-3 mr-2" />
                            Demo
                          </Button>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                  
                  {/* Project Info */}
                  <div className="p-6 space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h3 className="text-lg font-mono text-white group-hover:text-purple-400 transition-colors">
                          {project.title}
                        </h3>
                        <Badge className="bg-gray-800 text-gray-400 border-gray-600 font-mono text-xs ml-2 flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {project.timeline}
                        </Badge>
                      </div>
                      
                      <div className="text-xs font-mono text-gray-500">
                        $ {project.command}
                      </div>
                      
                      <p className="text-sm text-gray-300 font-mono line-clamp-3">
                        {project.description}
                      </p>
                    </div>
                    
                    {/* Tech Stack */}
                    <div className="flex flex-wrap gap-2">
                      {project.tech.slice(0, 3).map((tech) => (
                        <Badge key={tech} className="bg-gray-800 text-gray-300 border-gray-600 font-mono text-xs">
                          {tech}
                        </Badge>
                      ))}
                      {project.tech.length > 3 && (
                        <Badge className="bg-gray-800 text-gray-400 border-gray-600 font-mono text-xs">
                          +{project.tech.length - 3}
                        </Badge>
                      )}
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <Button 
                        size="sm" 
                        className="flex-1 bg-purple-500/20 text-purple-400 border-purple-500/50 hover:bg-purple-500/30 font-mono"
                        onClick={() => setSelectedProject(project)}
                      >
                        <Code className="w-3 h-3 mr-2" />
                        More
                      </Button>
                      <Button 
                        size="sm" 
                        className="flex-1 bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700 font-mono"
                      >
                        <Github className="w-3 h-3 mr-2" />
                        Code
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {filteredProjects.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="bg-gray-900 border border-gray-700 rounded-lg p-8">
              <p className="text-gray-400 font-mono">
                <span className="text-red-400">Error:</span> No projects found matching your criteria.
              </p>
              <p className="text-xs text-gray-500 font-mono mt-2">
                $ try different filters or check back later
              </p>
            </div>
          </motion.div>
        )}

        {/* View All Projects Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button 
              className="px-8 py-6 text-lg bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30 font-mono group"
            >
              <span className="group-hover:mr-2 transition-all">$ cat all-projects.md</span>
              <motion.span
                initial={{ x: 0 }}
                whileHover={{ x: 5 }}
                className="inline-block"
              >
                <ExternalLink className="w-4 h-4" />
              </motion.span>
            </Button>
          </motion.div>
        </motion.div>
      </div>

      {/* Project Detail Modal */}
      <Dialog open={!!selectedProject} onOpenChange={() => setSelectedProject(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-700 text-white">
          {selectedProject && (
            <>
              <DialogHeader className="border-b border-gray-700 pb-4">
                <DialogTitle className="text-2xl font-mono text-purple-400 flex items-center gap-2">
                  <Terminal className="w-5 h-5" />
                  {selectedProject.title}
                </DialogTitle>
                <DialogDescription className="text-lg text-gray-300 font-mono">
                  {selectedProject.fullDescription}
                </DialogDescription>
                <div className="text-xs font-mono text-gray-500 pt-2">
                  $ {selectedProject.command}
                </div>
              </DialogHeader>
              
              <div className="grid md:grid-cols-2 gap-6 mt-6">
                <div className="space-y-4">
                  <ImageWithFallback
                    src={selectedProject.image}
                    alt={selectedProject.title}
                    className="w-full h-64 object-cover rounded-lg border border-gray-700"
                  />
                  
                  <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 space-y-4">
                    <h4 className="font-mono text-green-400 flex items-center gap-2">
                      <Star className="w-4 h-4" />
                      Project Details
                    </h4>
                    <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400">Status:</span>
                        <Badge className={`text-xs ${
                          selectedProject.status === 'Completed' 
                            ? 'bg-green-500/20 text-green-400 border-green-500/50' 
                            : 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50'
                        }`}>
                          {selectedProject.status}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400">Timeline:</span>
                        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/50 text-xs">
                          {selectedProject.timeline}
                        </Badge>
                      </div>
                      <div className="col-span-2 flex items-center gap-2">
                        <span className="text-gray-400">Category:</span>
                        <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/50 text-xs">
                          {selectedProject.category}
                        </Badge>
                      </div>
                    </div>
                    
                    <div>
                      <h5 className="font-mono text-blue-400 mb-2 flex items-center gap-2">
                        <Code className="w-4 h-4" />
                        Tech Stack
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {selectedProject.tech.map((tech) => (
                          <Badge key={tech} className="bg-gray-900 text-gray-300 border-gray-600 font-mono text-xs">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
                    <h4 className="font-mono text-yellow-400 mb-3 flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      Key Features
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-300 font-mono">
                      {selectedProject.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-green-400 mt-1">•</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
                    <h4 className="font-mono text-red-400 mb-3 flex items-center gap-2">
                      <CircleDot className="w-4 h-4" />
                      Technical Challenges
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-300 font-mono">
                      {selectedProject.challenges.map((challenge, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-red-400 mt-1">•</span>
                          {challenge}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex gap-3 pt-4">
                    <Button className="flex-1 bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30 font-mono">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Live Demo
                    </Button>
                    <Button className="flex-1 bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700 font-mono">
                      <Github className="w-4 h-4 mr-2" />
                      Source
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </section>
  );
}