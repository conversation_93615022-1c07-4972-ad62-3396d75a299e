import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Terminal, Cpu, CheckCircle, Loader2, Code, Database, Server, Play, Square } from 'lucide-react';

interface SplashScreenProps {
  onComplete: () => void;
}

interface CommandHistory {
  command: string;
  response: string;
  status: 'success' | 'error' | 'info';
  timestamp: string;
}

const requiredCommands = [
  { command: 'pnpm dev', description: 'Start frontend development server' },
  { command: 'node server.js', description: 'Start backend server' }
];

const matrixChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?VICKY';

// Typewriter component using Motion's natural typing
const TypewriterText = ({ 
  text, 
  className = '', 
  speed = 50, 
  onComplete 
}: { 
  text: string; 
  className?: string; 
  speed?: number; 
  onComplete?: () => void;
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(text.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, speed + Math.random() * 30); // Add natural variation
      
      return () => clearTimeout(timer);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, speed, onComplete]);

  useEffect(() => {
    setDisplayedText('');
    setCurrentIndex(0);
  }, [text]);

  return (
    <motion.span 
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {displayedText}
      <motion.span
        animate={{ opacity: [1, 0, 1] }}
        transition={{ duration: 0.8, repeat: Infinity }}
        className="text-green-400"
      >
        |
      </motion.span>
    </motion.span>
  );
};

export default function SplashScreen({ onComplete }: SplashScreenProps) {
  const [commandHistory, setCommandHistory] = useState<CommandHistory[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [completedCommands, setCompletedCommands] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showCursor, setShowCursor] = useState(true);
  const [matrixRain, setMatrixRain] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [isReady, setIsReady] = useState(false);
  const [showInitialMessages, setShowInitialMessages] = useState(false);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const terminalRef = useRef<HTMLDivElement>(null);

  const initialMessages = [
    'VICKY MOSAFAN FULLSTACK DEVELOPER - DEVELOPMENT ENVIRONMENT',
    '═══════════════════════════════════════════════════════════',
    'Welcome to the development environment!',
    'Please start the development servers to continue:',
    '1. Type "pnpm dev" to start the frontend server',
    '2. Type "node server.js" to start the backend server',
    '─────────────────────────────────────────────────────────'
  ];

  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Start typing initial messages after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowInitialMessages(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Matrix rain effect
  useEffect(() => {
    const generateMatrixRain = () => {
      const newRain = [];
      for (let i = 0; i < 25; i++) {
        newRain.push(matrixChars[Math.floor(Math.random() * matrixChars.length)]);
      }
      setMatrixRain(newRain);
    };

    generateMatrixRain();
    const interval = setInterval(generateMatrixRain, 200);
    return () => clearInterval(interval);
  }, []);

  // Auto-scroll to bottom when new commands are added
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [commandHistory, currentMessageIndex]);

  // Handle typing initial messages
  const handleMessageComplete = () => {
    if (currentMessageIndex < initialMessages.length) {
      const newEntry: CommandHistory = {
        command: 'system',
        response: initialMessages[currentMessageIndex],
        status: 'info',
        timestamp: new Date().toLocaleTimeString()
      };
      
      setCommandHistory(prev => [...prev, newEntry]);
      setCurrentMessageIndex(prev => prev + 1);
    }
  };

  const handleCommand = async (command: string) => {
    if (!command.trim()) return;

    setIsProcessing(true);
    const timestamp = new Date().toLocaleTimeString();

    // Add the command to history
    const newCommand: CommandHistory = {
      command: `$ ${command}`,
      response: '',
      status: 'info',
      timestamp
    };

    setCommandHistory(prev => [...prev, newCommand]);

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    let response = '';
    let status: 'success' | 'error' | 'info' = 'error';

    // Check if command is valid
    if (command === 'pnpm dev') {
      if (completedCommands.includes('pnpm dev')) {
        response = '⚠️  Frontend server is already running on http://localhost:3000';
        status = 'info';
      } else {
        response = '✅ Frontend server started successfully!\n🚀 Local: http://localhost:3000\n📦 Building for development...';
        status = 'success';
        setCompletedCommands(prev => [...prev, 'pnpm dev']);
        setCurrentStep(1);
      }
    } else if (command === 'node server.js') {
      if (completedCommands.includes('node server.js')) {
        response = '⚠️  Backend server is already running on http://localhost:8000';
        status = 'info';
      } else {
        response = '✅ Backend server started successfully!\n🚀 Server running on http://localhost:8000\n📡 API endpoints ready...';
        status = 'success';
        setCompletedCommands(prev => [...prev, 'node server.js']);
        setCurrentStep(2);
      }
    } else if (command === 'help') {
      response = 'Available commands:\n• pnpm dev - Start frontend development server\n• node server.js - Start backend server\n• clear - Clear terminal\n• help - Show this help message';
      status = 'info';
    } else if (command === 'clear') {
      setCommandHistory([]);
      setIsProcessing(false);
      return;
    } else if (command === 'ls' || command === 'dir') {
      response = 'portfolio/\n├── package.json\n├── server.js\n├── src/\n└── public/';
      status = 'info';
    } else if (command.startsWith('npm') || command.startsWith('yarn')) {
      response = '❌ Please use "pnpm dev" to start the frontend server';
      status = 'error';
    } else {
      response = `❌ Command not found: ${command}\nType "help" for available commands`;
      status = 'error';
    }

    // Add response to history
    const responseEntry: CommandHistory = {
      command: 'system',
      response,
      status,
      timestamp
    };

    setCommandHistory(prev => [...prev, responseEntry]);

    // Check if both commands are completed
    const updatedCompleted = command === 'pnpm dev' || command === 'node server.js' 
      ? [...completedCommands, command].filter((cmd, index, arr) => arr.indexOf(cmd) === index)
      : completedCommands;

    if (updatedCompleted.length === 2) {
      setTimeout(() => {
        const readyEntry: CommandHistory = {
          command: 'system',
          response: '🎉 Both servers are running successfully!\n🚀 Development environment ready!\n📱 Launching portfolio application...',
          status: 'success',
          timestamp: new Date().toLocaleTimeString()
        };
        
        setCommandHistory(prev => [...prev, readyEntry]);
        setIsReady(true);
        
        setTimeout(() => {
          onComplete();
        }, 2000);
      }, 1000);
    }

    setIsProcessing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !isProcessing) {
      handleCommand(currentInput);
      setCurrentInput('');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentInput(e.target.value);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white flex items-center justify-center z-50 p-4"
    >
      {/* Matrix Rain Background */}
      <div className="absolute inset-0 overflow-hidden opacity-15">
        <div className="absolute inset-0 grid grid-cols-8 md:grid-cols-12 gap-1">
          {matrixRain.map((char, index) => (
            <motion.div
              key={index}
              animate={{
                y: [0, window.innerHeight || 1000],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: index * 0.1,
                ease: "linear"
              }}
              className="text-green-400 font-mono text-sm"
            >
              {char}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Power Grid Background */}
      <div className="absolute inset-0 opacity-10">
        <motion.div
          animate={{
            backgroundPosition: ['0% 0%', '100% 100%']
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(45deg, transparent 25%, rgba(34, 197, 94, 0.1) 25%, rgba(34, 197, 94, 0.1) 50%, transparent 50%, transparent 75%, rgba(34, 197, 94, 0.1) 75%),
              linear-gradient(-45deg, transparent 25%, rgba(59, 130, 246, 0.1) 25%, rgba(59, 130, 246, 0.1) 50%, transparent 50%, transparent 75%, rgba(59, 130, 246, 0.1) 75%)
            `,
            backgroundSize: '40px 40px'
          }}
        />
      </div>

      <div className="relative max-w-7xl mx-auto w-full">
        <div className="grid lg:grid-cols-12 gap-6 lg:gap-8 items-start">
          
          {/* Left Side - Status Panel */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="lg:col-span-4 space-y-4"
          >
            {/* Developer Info */}
            <motion.div
              animate={{
                boxShadow: [
                  '0 0 30px rgba(34, 197, 94, 0.2)',
                  '0 0 50px rgba(34, 197, 94, 0.4)',
                  '0 0 30px rgba(34, 197, 94, 0.2)'
                ]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="bg-gray-800/50 border border-green-500/30 rounded-lg p-4 lg:p-6 backdrop-blur-sm"
            >
              <div className="text-center space-y-3">
                <motion.h2 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-xl lg:text-2xl font-mono font-bold text-green-400"
                >
                  <TypewriterText 
                    text="VICKY MOSAFAN" 
                    speed={100}
                  />
                </motion.h2>
                <motion.p 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2 }}
                  className="text-lg text-gray-300 font-mono font-bold"
                >
                  <TypewriterText 
                    text="FULLSTACK DEVELOPER" 
                    speed={80}
                  />
                </motion.p>
                <motion.p 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 4 }}
                  className="text-gray-400 font-mono text-sm"
                >
                  <TypewriterText 
                    text="Interactive Development Environment" 
                    speed={60}
                  />
                </motion.p>
              </div>
            </motion.div>

            {/* Server Status */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 backdrop-blur-sm"
            >
              <h3 className="font-mono font-bold text-white mb-4 flex items-center gap-2">
                <Server className="w-4 h-4 text-blue-400" />
                Server Status
              </h3>
              
              <div className="space-y-3">
                {/* Frontend Status */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Code className="w-4 h-4 text-blue-400" />
                    <span className="font-mono text-sm">Frontend</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {completedCommands.includes('pnpm dev') ? (
                      <>
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="w-2 h-2 bg-green-500 rounded-full"
                        />
                        <span className="font-mono text-xs text-green-400">RUNNING</span>
                      </>
                    ) : (
                      <>
                        <div className="w-2 h-2 bg-gray-600 rounded-full" />
                        <span className="font-mono text-xs text-gray-500">STOPPED</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Backend Status */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Database className="w-4 h-4 text-green-400" />
                    <span className="font-mono text-sm">Backend</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {completedCommands.includes('node server.js') ? (
                      <>
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="w-2 h-2 bg-green-500 rounded-full"
                        />
                        <span className="font-mono text-xs text-green-400">RUNNING</span>
                      </>
                    ) : (
                      <>
                        <div className="w-2 h-2 bg-gray-600 rounded-full" />
                        <span className="font-mono text-xs text-gray-500">STOPPED</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Instructions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-gray-800/50 border border-yellow-500/30 rounded-lg p-4 backdrop-blur-sm"
            >
              <h3 className="font-mono font-bold text-yellow-400 mb-3 flex items-center gap-2">
                <Terminal className="w-4 h-4" />
                Required Commands
              </h3>
              
              <div className="space-y-2">
                {requiredCommands.map((cmd, index) => (
                  <div key={index} className="flex items-center gap-2">
                    {completedCommands.includes(cmd.command) ? (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    ) : (
                      <Square className="w-4 h-4 text-gray-500" />
                    )}
                    <div className="flex-1">
                      <code className="font-mono text-sm text-white bg-gray-900/50 px-2 py-1 rounded">
                        {cmd.command}
                      </code>
                      <p className="text-xs text-gray-400 mt-1">{cmd.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Right Side - Interactive Terminal */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:col-span-8 bg-gray-900 border border-green-500/30 rounded-lg shadow-2xl overflow-hidden"
          >
            {/* Terminal Header */}
            <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex gap-2">
                  <motion.div
                    animate={{ 
                      scale: [1, 1.2, 1],
                      boxShadow: ['0 0 5px rgba(239, 68, 68, 0.5)', '0 0 15px rgba(239, 68, 68, 0.8)', '0 0 5px rgba(239, 68, 68, 0.5)']
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="w-3 h-3 bg-red-500 rounded-full"
                  />
                  <motion.div
                    animate={{ 
                      scale: [1, 1.2, 1],
                      boxShadow: ['0 0 5px rgba(234, 179, 8, 0.5)', '0 0 15px rgba(234, 179, 8, 0.8)', '0 0 5px rgba(234, 179, 8, 0.5)']
                    }}
                    transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
                    className="w-3 h-3 bg-yellow-500 rounded-full"
                  />
                  <motion.div
                    animate={{ 
                      scale: [1, 1.2, 1],
                      boxShadow: ['0 0 5px rgba(34, 197, 94, 0.5)', '0 0 15px rgba(34, 197, 94, 0.8)', '0 0 5px rgba(34, 197, 94, 0.5)']
                    }}
                    transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
                    className="w-3 h-3 bg-green-500 rounded-full"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-mono text-gray-300">
                    vicky@development:~/portfolio$
                  </span>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <motion.div
                  animate={{ rotate: completedCommands.length < 2 ? 360 : 0 }}
                  transition={{ duration: 2, repeat: completedCommands.length < 2 ? Infinity : 0, ease: "linear" }}
                >
                  <Cpu className="w-4 h-4 text-blue-400" />
                </motion.div>
                <span className="text-xs font-mono text-gray-400">
                  {isReady ? 'READY' : 'WAITING'}
                </span>
              </div>
            </div>

            {/* Terminal Content */}
            <div 
              ref={terminalRef}
              className="p-4 lg:p-6 h-96 lg:h-[500px] overflow-y-auto font-mono text-sm bg-gray-900"
            >
              {/* Initial Messages with Typewriter */}
              {showInitialMessages && currentMessageIndex < initialMessages.length && (
                <div className="mb-4">
                  <TypewriterText 
                    text={initialMessages[currentMessageIndex]}
                    className="text-gray-300 block"
                    speed={30}
                    onComplete={handleMessageComplete}
                  />
                </div>
              )}

              {/* Command History */}
              <div className="space-y-2 mb-4">
                <AnimatePresence>
                  {commandHistory.map((entry, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-1"
                    >
                      {entry.command !== 'system' && (
                        <div className="flex items-center gap-2">
                          <span className="text-green-400">vicky@development:~/portfolio$</span>
                          <span className="text-white">{entry.command.replace('$ ', '')}</span>
                        </div>
                      )}
                      
                      {entry.response && (
                        <div className={`pl-4 whitespace-pre-wrap ${
                          entry.status === 'success' ? 'text-green-400' :
                          entry.status === 'error' ? 'text-red-400' :
                          'text-gray-300'
                        }`}>
                          {entry.response}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>

              {/* Input Line */}
              {!isReady && currentMessageIndex >= initialMessages.length && (
                <div className="flex items-center gap-2">
                  <span className="text-green-400">vicky@development:~/portfolio$</span>
                  <div className="flex-1 flex items-center">
                    <input
                      ref={inputRef}
                      type="text"
                      value={currentInput}
                      onChange={handleInputChange}
                      onKeyPress={handleKeyPress}
                      disabled={isProcessing}
                      className="bg-transparent border-none outline-none text-white font-mono text-sm flex-1"
                      placeholder={isProcessing ? "Processing..." : "Type your command..."}
                      autoComplete="off"
                      spellCheck={false}
                    />
                    {isProcessing ? (
                      <Loader2 className="w-4 h-4 text-yellow-400 animate-spin ml-2" />
                    ) : (
                      <motion.span
                        animate={{ opacity: showCursor ? 1 : 0 }}
                        className="bg-green-400 w-2 h-4 inline-block ml-1"
                      />
                    )}
                  </div>
                </div>
              )}

              {/* Ready State */}
              {isReady && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  className="mt-4 p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 rounded-lg text-center"
                >
                  <div className="flex items-center justify-center gap-2 text-green-400 mb-2">
                    <Play className="w-5 h-5" />
                    <TypewriterText 
                      text="DEVELOPMENT ENVIRONMENT READY!"
                      className="font-bold"
                      speed={40}
                    />
                  </div>
                  <TypewriterText 
                    text="Both servers are running successfully. Launching portfolio..."
                    className="text-gray-300 text-sm"
                    speed={35}
                  />
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}