import { useState } from 'react';
import { AnimatePresence } from 'motion/react';
import { Toaster } from './components/ui/sonner';

// Components
import Navigation from './components/Navigation';
import HeroSection from './components/HeroSection';
import SplashScreen from './components/SplashScreen';

export default function App() {
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  return (
    <div className="min-h-screen bg-background">
      <AnimatePresence mode="wait">
        {showSplash ? (
          <SplashScreen key="splash" onComplete={handleSplashComplete} />
        ) : (
          <div key="main">
            <Navigation />
            <HeroSection />
          </div>
        )}
      </AnimatePresence>
      
      {/* Global Toast Container */}
      <Toaster 
        position="bottom-right"
        toastOptions={{
          style: {
            background: 'hsl(var(--card))',
            border: '1px solid hsl(var(--border))',
            color: 'hsl(var(--card-foreground))',
          },
        }}
      />
    </div>
  );
}