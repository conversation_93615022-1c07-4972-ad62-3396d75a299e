import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';

import { 
  Target, 
  Users, 
  Zap, 
  Rocket, 
  TrendingUp, 
  ChevronDown,
  ChevronUp,
  Calendar,
  MapPin,
  Award,
  Code,
  Terminal,
  GitBranch,
  Coffee,
  Shield
} from 'lucide-react';

const experiences = [
  {
    period: '2023 - Present',
    title: 'Senior Full Stack Developer',
    company: 'Tech Innovation Co.',
    description: 'Leading development of scalable web applications using modern tech stack.',
    details: [
      'Led a team of 5 developers in building microservices architecture',
      'Improved application performance by 40% through optimization',
      'Implemented CI/CD pipelines reducing deployment time by 60%',
      'Mentored junior developers and conducted code reviews'
    ],
    skills: ['React', 'Node.js', 'AWS', 'Docker', 'TypeScript']
  },
  {
    period: '2021 - 2023',
    title: 'Full Stack Developer',
    company: 'Digital Solutions Inc.',
    description: 'Developed and maintained multiple client projects with focus on performance.',
    details: [
      'Built 15+ responsive web applications for various clients',
      'Integrated third-party APIs and payment gateways',
      'Optimized database queries improving response time by 35%',
      'Collaborated with UX/UI designers for pixel-perfect implementations'
    ],
    skills: ['Vue.js', 'Python', 'PostgreSQL', 'Redis', 'JavaScript']
  },
  {
    period: '2020 - 2021',
    title: 'Frontend Developer',
    company: 'StartupXYZ',
    description: 'Built responsive user interfaces and collaborated with design teams.',
    details: [
      'Developed mobile-first responsive websites',
      'Implemented modern UI components and design systems',
      'Collaborated with backend team for API integration',
      'Conducted user testing and implemented feedback'
    ],
    skills: ['HTML5', 'CSS3', 'JavaScript', 'Sass', 'Figma']
  },
];

const values = [
  {
    title: 'Code Quality',
    description: 'Writing clean, maintainable, and scalable code following best practices.',
    icon: Target,
    color: 'text-green-400',
    bgColor: 'bg-green-500/10',
    details: 'I believe in writing code that not only works but is also readable, testable, and maintainable. Following SOLID principles and design patterns to ensure long-term project success.',
    command: 'npm run lint'
  },
  {
    title: 'User Experience',
    description: 'Creating intuitive and accessible interfaces that users love to interact with.',
    icon: Users,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/10',
    details: 'Every design decision is made with the end user in mind. I focus on creating seamless, accessible, and enjoyable experiences that drive engagement and satisfaction.',
    command: 'yarn test:a11y'
  },
  {
    title: 'Performance',
    description: 'Optimizing applications for speed and efficiency across all devices.',
    icon: Zap,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/10',
    details: 'Performance is not just about fast loading times. It includes efficient algorithms, optimized assets, smart caching strategies, and responsive design for all devices.',
    command: 'npm run analyze'
  },
  {
    title: 'Innovation',
    description: 'Staying current with emerging technologies and implementing modern solutions.',
    icon: Rocket,
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/10',
    details: 'The tech landscape evolves rapidly. I continuously learn new technologies, experiment with cutting-edge tools, and evaluate their potential for solving real-world problems.',
    command: 'git checkout -b feature/new-tech'
  },
];

export default function AboutSection() {
  const [expandedExperience, setExpandedExperience] = useState<number | null>(null);
  const [expandedValue, setExpandedValue] = useState<number | null>(null);

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white relative overflow-hidden">
      {/* Terminal Grid Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '30px 30px'
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Badge className="mb-4 bg-green-500/20 text-green-400 border-green-500/50 hover:bg-green-500/30 font-mono">
              <Terminal className="w-3 h-3 mr-2" />
              $ cat about-vicky.md
            </Badge>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl lg:text-6xl font-mono mb-6"
          >
            <span className="text-gray-500">#</span>{' '}
            <span className="text-white">Passionate about creating</span>
            <br />
            <span className="text-green-400">digital_experiences</span>
            <motion.span
              animate={{ opacity: [1, 0, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="text-green-400"
            >
              _
            </motion.span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto font-mono"
          >
            <span className="text-gray-500">//</span> With over 4 years of experience in full-stack development,
            <br />
            <span className="text-gray-500">//</span> I specialize in building modern web applications that combine
            <br />
            <span className="text-gray-500">//</span> beautiful design with{' '}
            <span className="text-blue-400">robust functionality</span>.
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start mb-20">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Bio Section with Terminal Style */}
            <div className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                </div>
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-mono text-gray-300">about.js</span>
                </div>
              </div>

              {/* Terminal Content */}
              <div className="p-6 space-y-4 font-mono text-sm">
                <motion.div
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 1, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="space-y-2"
                >
                  <div className="text-gray-500">{"// Professional Bio"}</div>
                  <div className="text-blue-400">const <span className="text-white">developer</span> = {"{"}</div>
                  <div className="pl-4 space-y-1">
                    <div><span className="text-green-400">name</span>: <span className="text-yellow-400">"Vicky Mosafan"</span>,</div>
                    <div><span className="text-green-400">role</span>: <span className="text-yellow-400">"Full Stack Developer"</span>,</div>
                    <div><span className="text-green-400">passion</span>: <span className="text-yellow-400">"Turning complex problems into simple solutions"</span>,</div>
                    <div><span className="text-green-400">mission</span>: <span className="text-yellow-400">"Crafting exceptional digital experiences"</span></div>
                  </div>
                  <div className="text-blue-400">{"}"}</div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 1, delay: 0.6 }}
                  viewport={{ once: true }}
                  className="space-y-2"
                >
                  <div className="text-gray-500">{"// Personal Statement"}</div>
                  <div className="text-gray-300">
                    When I'm not coding, you can find me exploring new technologies,
                    contributing to open-source projects, or sharing knowledge with 
                    the developer community.
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Interactive Values Grid */}
            <div className="space-y-4">
              <h3 className="text-lg font-mono text-green-400 flex items-center gap-2">
                <GitBranch className="w-5 h-5" />
                $ ./core-values --list
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {values.map((value, index) => {
                  const IconComponent = value.icon;
                  return (
                    <motion.div
                      key={value.title}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      className="bg-gray-900 border border-gray-700 rounded-lg p-4 hover:border-green-500/30 transition-all cursor-pointer group"
                      onClick={() => setExpandedValue(expandedValue === index ? null : index)}
                    >
                      <div className="flex items-start gap-3">
                        <motion.div 
                          className={`p-2 rounded-lg ${value.bgColor} border border-gray-600 group-hover:scale-110 transition-transform`}
                          whileHover={{ rotate: [0, -5, 5, 0] }}
                          transition={{ duration: 0.5 }}
                        >
                          <IconComponent className={`w-4 h-4 ${value.color}`} />
                        </motion.div>
                        <div className="flex-1">
                          <h4 className="font-mono text-white mb-2 group-hover:text-green-400 transition-colors">
                            {value.title}
                          </h4>
                          <p className="text-xs text-gray-400 font-mono mb-2">{value.description}</p>
                          <div className="text-xs text-gray-500 font-mono">
                            $ {value.command}
                          </div>
                          
                          <AnimatePresence>
                            {expandedValue === index && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                                className="mt-3 pt-3 border-t border-gray-700"
                              >
                                <p className="text-xs text-gray-300 font-mono">{value.details}</p>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                        <motion.div
                          animate={{ rotate: expandedValue === index ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                          className="text-gray-400 group-hover:text-green-400 transition-colors"
                        >
                          <ChevronDown className="w-4 h-4" />
                        </motion.div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Interactive Experience Timeline */}
            <div className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center gap-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                </div>
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-mono text-gray-300">experience-log.md</span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="font-mono text-green-400 mb-6 flex items-center gap-2">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                  >
                    <TrendingUp className="w-5 h-5" />
                  </motion.div>
                  $ git log --oneline --graph
                </h3>
                
                <div className="space-y-6">
                  {experiences.map((exp, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="border-l-2 border-green-500/30 pl-6 pb-6 relative group hover:border-green-400 transition-colors"
                    >
                      {/* Timeline dot */}
                      <motion.div 
                        className="absolute -left-2 top-0 w-4 h-4 bg-green-400 rounded-full border-2 border-gray-900 group-hover:scale-125 transition-transform flex items-center justify-center"
                        whileHover={{ scale: 1.3 }}
                      >
                        <motion.div
                          className="w-1.5 h-1.5 bg-gray-900 rounded-full"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                      </motion.div>
                      
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/50 font-mono text-xs">
                            <Calendar className="w-3 h-3 mr-1" />
                            {exp.period}
                          </Badge>
                        </div>
                        
                        <h4 className="font-mono text-white group-hover:text-green-400 transition-colors">
                          {exp.title}
                        </h4>
                        
                        <p className="text-sm text-gray-400 font-mono flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {exp.company}
                        </p>
                        
                        <p className="text-sm text-gray-300 font-mono">{exp.description}</p>
                        
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs font-mono p-2 h-auto text-green-400 hover:bg-green-500/10 hover:text-green-300 transition-colors border border-green-500/30 hover:border-green-400"
                            onClick={() => setExpandedExperience(expandedExperience === index ? null : index)}
                          >
                            <span className="flex items-center gap-1">
                              {expandedExperience === index ? (
                                <>
                                  <ChevronUp className="w-3 h-3" />
                                  $ less
                                </>
                              ) : (
                                <>
                                  <ChevronDown className="w-3 h-3" />
                                  $ more
                                </>
                              )}
                            </span>
                          </Button>
                        </motion.div>
                        
                        <AnimatePresence>
                          {expandedExperience === index && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="space-y-4 pl-4 border-l border-gray-700"
                            >
                              <div>
                                <h5 className="text-xs font-mono text-green-400 mb-2 flex items-center gap-1">
                                  <Award className="w-3 h-3" />
                                  // Key Achievements
                                </h5>
                                <ul className="text-xs text-gray-300 space-y-1 font-mono">
                                  {exp.details.map((detail, detailIndex) => (
                                    <motion.li
                                      key={detailIndex}
                                      initial={{ opacity: 0, x: 10 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ delay: detailIndex * 0.1 }}
                                      className="flex items-start gap-2"
                                    >
                                      <span className="text-green-400 mt-1">•</span>
                                      {detail}
                                    </motion.li>
                                  ))}
                                </ul>
                              </div>
                              
                              <div>
                                <h5 className="text-xs font-mono text-blue-400 mb-2 flex items-center gap-1">
                                  <Code className="w-3 h-3" />
                                  // Tech Stack
                                </h5>
                                <div className="flex flex-wrap gap-1">
                                  {exp.skills.map((skill, skillIndex) => (
                                    <motion.span
                                      key={skill}
                                      initial={{ opacity: 0, scale: 0.8 }}
                                      animate={{ opacity: 1, scale: 1 }}
                                      transition={{ delay: skillIndex * 0.05 }}
                                      whileHover={{ scale: 1.05 }}
                                    >
                                      <Badge className="text-xs font-mono bg-gray-800 text-purple-400 border-purple-500/30 hover:bg-purple-500/20 hover:border-purple-400 transition-colors">
                                        {skill}
                                      </Badge>
                                    </motion.span>
                                  ))}
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}